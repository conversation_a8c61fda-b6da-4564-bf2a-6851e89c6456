#!/usr/bin/env python3
"""
Performance Benchmark Suite for Glassdoor Scrapers
Compares original vs optimized scraper performance
"""

import time
import asyncio
import statistics
import psutil
import gc
from typing import Dict, List, Tuple
import matplotlib.pyplot as plt
import pandas as pd
from datetime import datetime

# Import both scrapers
from new_glassdoor import GlassdoorScraper, ScraperConfig
from optimized_glassdoor import OptimizedGlassdoorScraper, OptimizedScraperConfig

class PerformanceBenchmark:
    """Comprehensive performance benchmarking suite"""
    
    def __init__(self):
        self.results = {
            'original': [],
            'optimized': []
        }
        self.test_cases = [
            ("Data Scientist", "Mumbai", 5),
            ("Software Engineer", "Bangalore", 10),
            ("Product Manager", "Delhi", 8),
            ("Data Analyst", "Pune", 6)
        ]
    
    def run_comprehensive_benchmark(self) -> Dict:
        """Run comprehensive performance comparison"""
        print("🚀 Starting Comprehensive Performance Benchmark")
        print("=" * 60)
        
        # Initialize scrapers
        original_config = ScraperConfig()
        optimized_config = OptimizedScraperConfig()
        
        original_scraper = GlassdoorScraper(original_config)
        optimized_scraper = OptimizedGlassdoorScraper(optimized_config)
        
        benchmark_results = {
            'test_summary': {},
            'detailed_results': {},
            'performance_metrics': {},
            'resource_usage': {}
        }
        
        try:
            # Run benchmarks for each test case
            for i, (job_title, location, num_jobs) in enumerate(self.test_cases, 1):
                print(f"\n📊 Test Case {i}: {job_title} in {location} ({num_jobs} jobs)")
                print("-" * 50)
                
                # Test original scraper
                print("🔄 Testing Original Scraper...")
                original_result = self._benchmark_scraper(
                    original_scraper, 'original', job_title, location, num_jobs
                )
                
                # Clean up memory between tests
                gc.collect()
                time.sleep(2)
                
                # Test optimized scraper
                print("⚡ Testing Optimized Scraper...")
                optimized_result = self._benchmark_scraper(
                    optimized_scraper, 'optimized', job_title, location, num_jobs
                )
                
                # Store results
                test_key = f"{job_title}_{location}_{num_jobs}"
                benchmark_results['detailed_results'][test_key] = {
                    'original': original_result,
                    'optimized': optimized_result,
                    'improvement': self._calculate_improvement(original_result, optimized_result)
                }
                
                # Print comparison
                self._print_comparison(original_result, optimized_result)
                
                # Clean up
                gc.collect()
                time.sleep(3)
            
            # Calculate overall statistics
            benchmark_results['test_summary'] = self._calculate_summary_stats()
            benchmark_results['performance_metrics'] = self._calculate_performance_metrics()
            
            # Generate report
            self._generate_performance_report(benchmark_results)
            
            return benchmark_results
            
        finally:
            # Cleanup
            try:
                original_scraper.executor.shutdown(wait=True)
            except:
                pass
            try:
                optimized_scraper.cleanup()
            except:
                pass
    
    def _benchmark_scraper(self, scraper, scraper_type: str, job_title: str, 
                          location: str, num_jobs: int) -> Dict:
        """Benchmark a single scraper"""
        # Memory before
        process = psutil.Process()
        memory_before = process.memory_info().rss / 1024 / 1024
        cpu_before = process.cpu_percent()
        
        # Time the scraping
        start_time = time.time()
        
        try:
            if scraper_type == 'optimized':
                result = scraper.scrape_jobs_sync(job_title, location, num_jobs)
            else:
                result = scraper.scrape_jobs_parallel(job_title, location, num_jobs)
            
            end_time = time.time()
            
            # Memory after
            memory_after = process.memory_info().rss / 1024 / 1024
            cpu_after = process.cpu_percent()
            
            # Extract metrics
            execution_time = end_time - start_time
            scraped_jobs = len(result.get('scraped_jobs', []))
            failed_jobs = result.get('metadata', {}).get('failed_jobs', 0)
            
            benchmark_result = {
                'execution_time': execution_time,
                'scraped_jobs': scraped_jobs,
                'failed_jobs': failed_jobs,
                'success_rate': scraped_jobs / num_jobs if num_jobs > 0 else 0,
                'avg_time_per_job': execution_time / scraped_jobs if scraped_jobs > 0 else float('inf'),
                'memory_usage': {
                    'before_mb': memory_before,
                    'after_mb': memory_after,
                    'peak_mb': memory_after,
                    'delta_mb': memory_after - memory_before
                },
                'cpu_usage': {
                    'before_percent': cpu_before,
                    'after_percent': cpu_after
                },
                'metadata': result.get('metadata', {})
            }
            
            # Add scraper-specific metrics
            if scraper_type == 'optimized':
                perf_stats = scraper.get_performance_stats()
                benchmark_result['cache_hit_rate'] = perf_stats.get('cache_hits', 0) / max(1, perf_stats.get('cache_hits', 0) + perf_stats.get('cache_misses', 0))
                benchmark_result['browser_pool_efficiency'] = perf_stats.get('browser_pool_size', 0)
            
            self.results[scraper_type].append(benchmark_result)
            return benchmark_result
            
        except Exception as e:
            print(f"❌ Scraper failed: {str(e)}")
            return {
                'execution_time': float('inf'),
                'scraped_jobs': 0,
                'failed_jobs': num_jobs,
                'success_rate': 0,
                'avg_time_per_job': float('inf'),
                'error': str(e)
            }
    
    def _calculate_improvement(self, original: Dict, optimized: Dict) -> Dict:
        """Calculate performance improvement metrics"""
        improvements = {}
        
        # Time improvement
        if original['execution_time'] > 0:
            time_improvement = ((original['execution_time'] - optimized['execution_time']) / 
                              original['execution_time']) * 100
            improvements['time_improvement_percent'] = time_improvement
        
        # Throughput improvement
        original_throughput = original['scraped_jobs'] / original['execution_time'] if original['execution_time'] > 0 else 0
        optimized_throughput = optimized['scraped_jobs'] / optimized['execution_time'] if optimized['execution_time'] > 0 else 0
        
        if original_throughput > 0:
            throughput_improvement = ((optimized_throughput - original_throughput) / 
                                    original_throughput) * 100
            improvements['throughput_improvement_percent'] = throughput_improvement
        
        # Success rate improvement
        improvements['success_rate_improvement'] = optimized['success_rate'] - original['success_rate']
        
        # Memory efficiency
        original_memory = original.get('memory_usage', {}).get('delta_mb', 0)
        optimized_memory = optimized.get('memory_usage', {}).get('delta_mb', 0)
        
        if original_memory > 0:
            memory_improvement = ((original_memory - optimized_memory) / original_memory) * 100
            improvements['memory_improvement_percent'] = memory_improvement
        
        return improvements
    
    def _print_comparison(self, original: Dict, optimized: Dict):
        """Print performance comparison"""
        print(f"⏱️  Execution Time:")
        print(f"   Original:  {original['execution_time']:.2f}s")
        print(f"   Optimized: {optimized['execution_time']:.2f}s")
        
        if original['execution_time'] > 0:
            improvement = ((original['execution_time'] - optimized['execution_time']) / 
                          original['execution_time']) * 100
            print(f"   Improvement: {improvement:.1f}%")
        
        print(f"📊 Jobs Scraped:")
        print(f"   Original:  {original['scraped_jobs']}")
        print(f"   Optimized: {optimized['scraped_jobs']}")
        
        print(f"🎯 Success Rate:")
        print(f"   Original:  {original['success_rate']:.1%}")
        print(f"   Optimized: {optimized['success_rate']:.1%}")
        
        print(f"💾 Memory Usage:")
        print(f"   Original:  {original.get('memory_usage', {}).get('delta_mb', 0):.1f}MB")
        print(f"   Optimized: {optimized.get('memory_usage', {}).get('delta_mb', 0):.1f}MB")
    
    def _calculate_summary_stats(self) -> Dict:
        """Calculate overall summary statistics"""
        summary = {}
        
        for scraper_type in ['original', 'optimized']:
            results = self.results[scraper_type]
            if results:
                execution_times = [r['execution_time'] for r in results if r['execution_time'] != float('inf')]
                success_rates = [r['success_rate'] for r in results]
                
                summary[scraper_type] = {
                    'avg_execution_time': statistics.mean(execution_times) if execution_times else 0,
                    'median_execution_time': statistics.median(execution_times) if execution_times else 0,
                    'avg_success_rate': statistics.mean(success_rates) if success_rates else 0,
                    'total_jobs_scraped': sum(r['scraped_jobs'] for r in results),
                    'total_failed_jobs': sum(r['failed_jobs'] for r in results)
                }
        
        return summary
    
    def _calculate_performance_metrics(self) -> Dict:
        """Calculate key performance metrics"""
        original_stats = self.results['original']
        optimized_stats = self.results['optimized']
        
        if not original_stats or not optimized_stats:
            return {}
        
        # Average improvements
        avg_time_improvement = statistics.mean([
            ((o['execution_time'] - opt['execution_time']) / o['execution_time']) * 100
            for o, opt in zip(original_stats, optimized_stats)
            if o['execution_time'] > 0
        ])
        
        avg_throughput_original = statistics.mean([
            r['scraped_jobs'] / r['execution_time'] 
            for r in original_stats if r['execution_time'] > 0
        ])
        
        avg_throughput_optimized = statistics.mean([
            r['scraped_jobs'] / r['execution_time'] 
            for r in optimized_stats if r['execution_time'] > 0
        ])
        
        return {
            'avg_time_improvement_percent': avg_time_improvement,
            'avg_throughput_improvement_percent': ((avg_throughput_optimized - avg_throughput_original) / avg_throughput_original) * 100 if avg_throughput_original > 0 else 0,
            'target_50_percent_improvement': avg_time_improvement >= 50,
            'performance_grade': 'A' if avg_time_improvement >= 50 else 'B' if avg_time_improvement >= 30 else 'C'
        }
    
    def _generate_performance_report(self, results: Dict):
        """Generate comprehensive performance report"""
        print("\n" + "="*60)
        print("📈 PERFORMANCE BENCHMARK REPORT")
        print("="*60)
        
        summary = results['test_summary']
        metrics = results['performance_metrics']
        
        print(f"\n🎯 OVERALL PERFORMANCE IMPROVEMENT:")
        print(f"   Average Time Improvement: {metrics.get('avg_time_improvement_percent', 0):.1f}%")
        print(f"   Average Throughput Improvement: {metrics.get('avg_throughput_improvement_percent', 0):.1f}%")
        print(f"   Performance Grade: {metrics.get('performance_grade', 'N/A')}")
        print(f"   Target 50% Improvement: {'✅ ACHIEVED' if metrics.get('target_50_percent_improvement', False) else '❌ NOT ACHIEVED'}")
        
        print(f"\n📊 SUMMARY STATISTICS:")
        for scraper_type in ['original', 'optimized']:
            stats = summary.get(scraper_type, {})
            print(f"\n   {scraper_type.upper()} SCRAPER:")
            print(f"     Average Execution Time: {stats.get('avg_execution_time', 0):.2f}s")
            print(f"     Median Execution Time: {stats.get('median_execution_time', 0):.2f}s")
            print(f"     Average Success Rate: {stats.get('avg_success_rate', 0):.1%}")
            print(f"     Total Jobs Scraped: {stats.get('total_jobs_scraped', 0)}")
        
        # Save detailed results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        with open(f'benchmark_results_{timestamp}.json', 'w') as f:
            import json
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n💾 Detailed results saved to: benchmark_results_{timestamp}.json")

def main():
    """Run the performance benchmark"""
    benchmark = PerformanceBenchmark()
    results = benchmark.run_comprehensive_benchmark()
    
    print("\n🎉 Benchmark completed successfully!")
    return results

if __name__ == "__main__":
    main()
