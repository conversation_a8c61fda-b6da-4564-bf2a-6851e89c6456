#!/usr/bin/env python3
"""
Debug script to verify Glassdoor scraper workflow step by step.
This script will run the scraper in non-headless mode and provide detailed logging
for each step of the process.
"""

import time
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from new_glassdoor import GlassdoorScraper, ScraperConfig

# Enhanced logging setup
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('glassdoor_debug.log')
    ]
)
logger = logging.getLogger(__name__)

def debug_step_1_navigation(driver):
    """Step 1: Test initial navigation to Glassdoor"""
    logger.info("=== STEP 1: INITIAL NAVIGATION ===")
    
    try:
        logger.info("Navigating to https://www.glassdoor.co.in/Job/index.htm")
        driver.get("https://www.glassdoor.co.in/Job/index.htm")
        
        # Wait for page to load
        time.sleep(5)
        
        # Check current URL
        current_url = driver.current_url
        logger.info(f"Current URL: {current_url}")
        
        # Check if Glassdoor loaded
        if "glassdoor" in current_url.lower():
            logger.info("✅ Successfully navigated to Glassdoor")
            
            # Check page title
            title = driver.title
            logger.info(f"Page title: {title}")
            
            # Take screenshot for verification
            driver.save_screenshot("step1_navigation.png")
            logger.info("Screenshot saved: step1_navigation.png")
            
            return True
        else:
            logger.error("❌ Failed to navigate to Glassdoor")
            return False
            
    except Exception as e:
        logger.error(f"❌ Navigation failed with error: {str(e)}")
        return False

def debug_step_2_search_form(driver, job_title="Data Scientist", location="Mumbai"):
    """Step 2: Test search form interaction"""
    logger.info("=== STEP 2: SEARCH FORM INTERACTION ===")
    
    try:
        # Look for job title input
        logger.info("Looking for job title input field...")
        job_input = None
        job_selectors = [
            "searchBar-jobTitle",
            "KeywordSearch",
            "sc.keyword",
            "job-search-bar"
        ]
        
        for selector in job_selectors:
            try:
                job_input = driver.find_element(By.ID, selector)
                logger.info(f"✅ Found job title input with ID: {selector}")
                break
            except NoSuchElementException:
                logger.debug(f"Job input not found with ID: {selector}")
                continue
        
        if not job_input:
            # Try CSS selectors
            css_selectors = [
                "input[placeholder*='job title']",
                "input[placeholder*='Job Title']",
                "input[name*='keyword']",
                "input[data-test*='job']"
            ]
            
            for selector in css_selectors:
                try:
                    job_input = driver.find_element(By.CSS_SELECTOR, selector)
                    logger.info(f"✅ Found job title input with CSS: {selector}")
                    break
                except NoSuchElementException:
                    continue
        
        if not job_input:
            logger.error("❌ Could not find job title input field")
            return False
        
        # Look for location input
        logger.info("Looking for location input field...")
        location_input = None
        location_selectors = [
            "searchBar-location",
            "LocationSearch", 
            "sc.location",
            "location-search-bar"
        ]
        
        for selector in location_selectors:
            try:
                location_input = driver.find_element(By.ID, selector)
                logger.info(f"✅ Found location input with ID: {selector}")
                break
            except NoSuchElementException:
                continue
        
        if not location_input:
            # Try CSS selectors
            css_selectors = [
                "input[placeholder*='location']",
                "input[placeholder*='Location']", 
                "input[name*='location']",
                "input[data-test*='location']"
            ]
            
            for selector in css_selectors:
                try:
                    location_input = driver.find_element(By.CSS_SELECTOR, selector)
                    logger.info(f"✅ Found location input with CSS: {selector}")
                    break
                except NoSuchElementException:
                    continue
        
        if not location_input:
            logger.error("❌ Could not find location input field")
            return False
        
        # Fill the form
        logger.info(f"Filling job title: {job_title}")
        job_input.clear()
        job_input.send_keys(job_title)
        time.sleep(1)
        
        logger.info(f"Filling location: {location}")
        location_input.clear()
        location_input.send_keys(location)
        time.sleep(1)
        
        # Take screenshot before submitting
        driver.save_screenshot("step2_form_filled.png")
        logger.info("Screenshot saved: step2_form_filled.png")
        
        # Submit the form (try ENTER first)
        logger.info("Submitting form with ENTER key...")
        location_input.send_keys(Keys.RETURN)
        time.sleep(5)
        
        # Check if search worked
        current_url = driver.current_url
        logger.info(f"URL after ENTER: {current_url}")
        
        if "/Job/" in current_url:
            logger.info("✅ Search successful via ENTER")
            driver.save_screenshot("step2_search_success.png")
            return True
        
        # Fallback: Try clicking search button
        logger.info("ENTER didn't work, trying search button...")
        search_buttons = [
            "//button[@type='submit']",
            "//button[contains(text(), 'Search')]",
            "//input[@type='submit']",
            "//button[contains(@class, 'search')]"
        ]
        
        for button_xpath in search_buttons:
            try:
                search_btn = driver.find_element(By.XPATH, button_xpath)
                logger.info(f"Found search button: {button_xpath}")
                driver.execute_script("arguments[0].click();", search_btn)
                time.sleep(5)
                
                current_url = driver.current_url
                if "/Job/" in current_url:
                    logger.info("✅ Search successful via button click")
                    driver.save_screenshot("step2_search_success.png")
                    return True
                    
            except NoSuchElementException:
                continue
        
        logger.error("❌ Could not submit search form")
        return False
        
    except Exception as e:
        logger.error(f"❌ Search form interaction failed: {str(e)}")
        return False

def debug_step_3_job_urls(driver):
    """Step 3: Test job card URL collection"""
    logger.info("=== STEP 3: JOB CARD URL COLLECTION ===")
    
    try:
        # Wait for job listings to load
        time.sleep(3)
        
        # Take screenshot of search results
        driver.save_screenshot("step3_search_results.png")
        logger.info("Screenshot saved: step3_search_results.png")
        
        # Test different selectors for job cards
        job_selectors = [
            "a.JobCard_trackingLink__HMyun[data-test='job-link']",
            "a[data-test='job-title']",
            "a.JobCard_jobTitle__GLyJ1",
            ".JobCard_jobTitle__rw2J1 a",
            "a[href*='/job-listing/']",
            "a[href*='jl=']"
        ]
        
        found_jobs = []
        
        for selector in job_selectors:
            try:
                job_links = driver.find_elements(By.CSS_SELECTOR, selector)
                if job_links:
                    logger.info(f"✅ Found {len(job_links)} job links with selector: {selector}")
                    
                    # Extract URLs
                    for i, link in enumerate(job_links[:5]):  # Check first 5
                        try:
                            href = link.get_attribute('href')
                            title = link.text.strip()
                            logger.info(f"  Job {i+1}: {title[:50]}... -> {href}")
                            found_jobs.append((title, href))
                        except Exception as e:
                            logger.debug(f"Error extracting job {i+1}: {e}")
                    
                    break
                else:
                    logger.debug(f"No job links found with selector: {selector}")
                    
            except Exception as e:
                logger.debug(f"Error with selector {selector}: {e}")
        
        if found_jobs:
            logger.info(f"✅ Successfully found {len(found_jobs)} job URLs")
            return found_jobs
        else:
            logger.error("❌ No job URLs found with any selector")
            return []
            
    except Exception as e:
        logger.error(f"❌ Job URL collection failed: {str(e)}")
        return []

def debug_step_4_individual_job(driver, job_url):
    """Step 4: Test individual job page processing"""
    logger.info("=== STEP 4: INDIVIDUAL JOB PAGE PROCESSING ===")
    
    try:
        logger.info(f"Navigating to job: {job_url}")
        driver.get(job_url)
        time.sleep(3)
        
        # Take screenshot of job page
        driver.save_screenshot("step4_job_page.png")
        logger.info("Screenshot saved: step4_job_page.png")
        
        # Check for show more button
        show_more_selectors = [
            "button[data-test='show-more-cta']",
            "button[data-test='show-more']",
            "button.css-1gpqj0y",
            "[class*='show-more']",
            "//button[contains(text(), 'Show more')]",
            "//button[contains(text(), 'Read more')]"
        ]
        
        show_more_found = False
        for selector in show_more_selectors:
            try:
                if selector.startswith("//"):
                    show_more = driver.find_element(By.XPATH, selector)
                else:
                    show_more = driver.find_element(By.CSS_SELECTOR, selector)
                
                if show_more.is_displayed() and show_more.is_enabled():
                    logger.info(f"✅ Found show more button: {selector}")
                    logger.info("Clicking show more button...")
                    driver.execute_script("arguments[0].click();", show_more)
                    time.sleep(2)
                    show_more_found = True
                    
                    # Take screenshot after expanding
                    driver.save_screenshot("step4_expanded.png")
                    logger.info("Screenshot saved: step4_expanded.png")
                    break
                    
            except NoSuchElementException:
                continue
            except Exception as e:
                logger.debug(f"Error with show more selector {selector}: {e}")
        
        if not show_more_found:
            logger.info("ℹ️  No show more button found (content may already be expanded)")
        
        # Test job data extraction
        logger.info("Testing job data extraction...")
        
        # Job title
        title_selectors = [
            "h1[id*='job-title']",
            "h1[data-test='job-title']",
            ".JobDetails_jobTitle__*",
            "h1"
        ]
        
        title = None
        for selector in title_selectors:
            try:
                if '*' in selector:
                    # Handle wildcard selectors
                    elements = driver.find_elements(By.CSS_SELECTOR, selector.replace('*', ''))
                    if elements:
                        title = elements[0].text.strip()
                        logger.info(f"✅ Found title with selector {selector}: {title}")
                        break
                else:
                    element = driver.find_element(By.CSS_SELECTOR, selector)
                    title = element.text.strip()
                    logger.info(f"✅ Found title with selector {selector}: {title}")
                    break
            except:
                continue
        
        if not title:
            logger.error("❌ Could not extract job title")
            return False
        
        # Company name
        company_selectors = [
            ".EmployerProfile_employerNameHeading__bXBYr",
            "[data-test='employer-name']",
            ".EmployerProfile_profileContainer__* h4"
        ]
        
        company = None
        for selector in company_selectors:
            try:
                element = driver.find_element(By.CSS_SELECTOR, selector)
                company = element.text.strip()
                logger.info(f"✅ Found company with selector {selector}: {company}")
                break
            except:
                continue
        
        if not company:
            logger.info("ℹ️  Could not extract company name")
        
        # Job description
        desc_selectors = [
            ".JobDetails_jobDescription__uW_fK",
            "[data-test='jobDescriptionContent']",
            ".job-description"
        ]
        
        description = None
        for selector in desc_selectors:
            try:
                element = driver.find_element(By.CSS_SELECTOR, selector)
                description = element.text.strip()[:200] + "..."
                logger.info(f"✅ Found description with selector {selector}: {description}")
                break
            except:
                continue
        
        if not description:
            logger.error("❌ Could not extract job description")
            return False
        
        logger.info("✅ Individual job page processing successful")
        return True
        
    except Exception as e:
        logger.error(f"❌ Individual job processing failed: {str(e)}")
        return False

def main():
    """Main debug function"""
    logger.info("Starting Glassdoor scraper workflow debug...")
    
    # Create scraper with non-headless mode (already configured)
    config = ScraperConfig()
    scraper = GlassdoorScraper(config)
    
    # Create driver manually for debugging
    driver = scraper.driver_manager.create_driver()
    
    try:
        # Step 1: Navigation
        if not debug_step_1_navigation(driver):
            logger.error("Step 1 failed, stopping debug")
            return
        
        input("Press Enter to continue to Step 2...")
        
        # Step 2: Search form
        if not debug_step_2_search_form(driver):
            logger.error("Step 2 failed, stopping debug")
            return
        
        input("Press Enter to continue to Step 3...")
        
        # Step 3: Job URL collection
        job_urls = debug_step_3_job_urls(driver)
        if not job_urls:
            logger.error("Step 3 failed, stopping debug")
            return
        
        input("Press Enter to continue to Step 4...")
        
        # Step 4: Individual job processing (test first job)
        if job_urls:
            first_job_url = job_urls[0][1]  # Get URL from tuple
            if not debug_step_4_individual_job(driver, first_job_url):
                logger.error("Step 4 failed")
                return
        
        logger.info("🎉 All workflow steps completed successfully!")
        
    except KeyboardInterrupt:
        logger.info("Debug interrupted by user")
    except Exception as e:
        logger.error(f"Debug failed with error: {str(e)}")
    finally:
        input("Press Enter to close browser...")
        driver.quit()

if __name__ == "__main__":
    main()
