#!/usr/bin/env python3
"""
High-Performance Optimized Glassdoor Scraper
Implements advanced speed enhancement techniques including:
- Browser instance pooling
- Asynchronous processing
- Intelligent caching
- Resource optimization
- Memory management
"""

import asyncio
import json
import logging
import os
import time
import gc
import threading
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from contextlib import contextmanager
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set, Tuple, Any
from urllib.parse import urljoin
import random
from collections import deque
import weakref
import psutil

# Selenium imports
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException

# HTML parsing
from bs4 import BeautifulSoup

# Import base classes from original scraper
from new_glassdoor import <PERSON>raper<PERSON><PERSON>ger, <PERSON><PERSON><PERSON><PERSON><PERSON>, Human<PERSON>ehavi<PERSON>, JobPosting

@dataclass
class OptimizedScraperConfig:
    """Enhanced configuration for optimized scraper"""
    chrome_driver_path: str = '/Users/<USER>/Desktop/job_portal/chromedriver'
    
    # Performance settings
    max_workers: int = 8  # Increased from 3
    browser_pool_size: int = 4  # Browser instance pool
    max_concurrent_requests: int = 12  # HTTP request concurrency
    
    # Timeouts (optimized for speed)
    page_load_timeout: int = 15  # Reduced from 30
    implicit_wait: int = 5  # Reduced from 10
    request_timeout: int = 10
    
    # Caching settings
    enable_caching: bool = True
    cache_ttl_minutes: int = 30
    max_cache_size: int = 1000
    
    # Resource optimization
    disable_images: bool = True
    disable_css: bool = True
    disable_javascript: bool = False  # Keep for extraction
    enable_compression: bool = True
    
    # Rate limiting
    min_request_delay: float = 0.5  # Reduced from 3-7 seconds
    max_request_delay: float = 1.5
    
    # Memory management
    gc_frequency: int = 50  # Run GC every 50 operations
    max_memory_mb: int = 2048  # Memory limit

class BrowserPool:
    """High-performance browser instance pool with lifecycle management"""
    
    def __init__(self, config: OptimizedScraperConfig, logger: ScraperLogger):
        self.config = config
        self.logger = logger
        self.pool = deque()
        self.pool_lock = threading.Lock()
        self.active_browsers = weakref.WeakSet()
        self.creation_count = 0
        self.max_browser_age = timedelta(minutes=30)  # Refresh browsers every 30 min
        
    def _create_optimized_browser(self) -> webdriver.Chrome:
        """Create a highly optimized Chrome browser instance"""
        options = Options()
        
        # Performance optimizations
        options.add_argument('--headless=new')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-plugins')
        options.add_argument('--disable-background-networking')
        options.add_argument('--disable-background-timer-throttling')
        options.add_argument('--disable-backgrounding-occluded-windows')
        options.add_argument('--disable-renderer-backgrounding')
        options.add_argument('--disable-features=TranslateUI')
        options.add_argument('--disable-ipc-flooding-protection')
        options.add_argument('--disable-hang-monitor')
        options.add_argument('--disable-client-side-phishing-detection')
        options.add_argument('--disable-popup-blocking')
        options.add_argument('--disable-prompt-on-repost')
        options.add_argument('--disable-sync')
        options.add_argument('--disable-translate')
        options.add_argument('--disable-web-security')
        options.add_argument('--disable-features=VizDisplayCompositor')
        options.add_argument('--disable-logging')
        options.add_argument('--disable-default-apps')
        
        # Memory optimizations
        options.add_argument('--memory-pressure-off')
        options.add_argument('--max_old_space_size=4096')
        options.add_argument('--aggressive-cache-discard')
        
        # Network optimizations
        options.add_argument('--enable-features=NetworkService')
        options.add_argument('--enable-features=VizDisplayCompositor')
        
        # Resource blocking for speed
        prefs = {
            "profile.default_content_setting_values": {
                "images": 2 if self.config.disable_images else 1,
                "plugins": 2,
                "popups": 2,
                "geolocation": 2,
                "notifications": 2,
                "media_stream": 2,
            },
            "profile.managed_default_content_settings": {
                "images": 2 if self.config.disable_images else 1
            }
        }
        
        if self.config.disable_css:
            prefs["profile.default_content_setting_values"]["stylesheets"] = 2
            
        options.add_experimental_option("prefs", prefs)
        
        # Anti-detection (minimal for performance)
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument('--disable-blink-features=AutomationControlled')
        
        # Create driver
        service = Service(self.config.chrome_driver_path)
        driver = webdriver.Chrome(service=service, options=options)
        
        # Optimized timeouts
        driver.set_page_load_timeout(self.config.page_load_timeout)
        driver.implicitly_wait(self.config.implicit_wait)
        
        # Minimal anti-detection script
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        # Track creation
        self.creation_count += 1
        driver._creation_time = datetime.now()
        driver._request_count = 0
        
        self.logger.info(f"Created optimized browser instance #{self.creation_count}")
        return driver
    
    @contextmanager
    def get_browser(self):
        """Get a browser from the pool with automatic return"""
        browser = None
        try:
            with self.pool_lock:
                if self.pool:
                    browser = self.pool.popleft()
                    # Check if browser is still valid and not too old
                    if (hasattr(browser, '_creation_time') and 
                        datetime.now() - browser._creation_time > self.max_browser_age):
                        try:
                            browser.quit()
                        except:
                            pass
                        browser = None
                
                if browser is None:
                    browser = self._create_optimized_browser()
                
                self.active_browsers.add(browser)
            
            yield browser
            
        finally:
            if browser:
                try:
                    # Increment request count
                    browser._request_count = getattr(browser, '_request_count', 0) + 1
                    
                    # Return to pool if still valid and not overused
                    if (browser._request_count < 100 and  # Max 100 requests per browser
                        len(self.pool) < self.config.browser_pool_size):
                        with self.pool_lock:
                            self.pool.append(browser)
                    else:
                        browser.quit()
                except Exception as e:
                    self.logger.debug(f"Error returning browser to pool: {e}")
                    try:
                        browser.quit()
                    except:
                        pass
    
    def cleanup(self):
        """Clean up all browser instances"""
        with self.pool_lock:
            while self.pool:
                browser = self.pool.popleft()
                try:
                    browser.quit()
                except:
                    pass
        
        # Clean up active browsers
        for browser in list(self.active_browsers):
            try:
                browser.quit()
            except:
                pass

class JobCache:
    """High-performance job caching system"""
    
    def __init__(self, config: OptimizedScraperConfig):
        self.config = config
        self.cache = {}
        self.cache_times = {}
        self.access_times = {}
        self.lock = threading.Lock()
        
    def _is_expired(self, key: str) -> bool:
        """Check if cache entry is expired"""
        if key not in self.cache_times:
            return True
        
        age = datetime.now() - self.cache_times[key]
        return age > timedelta(minutes=self.config.cache_ttl_minutes)
    
    def get(self, url: str) -> Optional[Dict]:
        """Get cached job data"""
        if not self.config.enable_caching:
            return None
            
        with self.lock:
            if url in self.cache and not self._is_expired(url):
                self.access_times[url] = datetime.now()
                return self.cache[url]
        return None
    
    def set(self, url: str, job_data: Dict):
        """Cache job data with LRU eviction"""
        if not self.config.enable_caching:
            return
            
        with self.lock:
            # Evict old entries if cache is full
            if len(self.cache) >= self.config.max_cache_size:
                # Remove oldest accessed entry
                oldest_url = min(self.access_times.keys(), 
                               key=lambda k: self.access_times[k])
                self._remove_entry(oldest_url)
            
            self.cache[url] = job_data
            self.cache_times[url] = datetime.now()
            self.access_times[url] = datetime.now()
    
    def _remove_entry(self, url: str):
        """Remove cache entry"""
        self.cache.pop(url, None)
        self.cache_times.pop(url, None)
        self.access_times.pop(url, None)
    
    def cleanup_expired(self):
        """Remove expired entries"""
        with self.lock:
            expired_keys = [key for key in self.cache.keys() if self._is_expired(key)]
            for key in expired_keys:
                self._remove_entry(key)

class MemoryManager:
    """Memory management and optimization"""
    
    def __init__(self, config: OptimizedScraperConfig, logger: ScraperLogger):
        self.config = config
        self.logger = logger
        self.operation_count = 0
        
    def check_memory(self) -> bool:
        """Check if memory usage is within limits"""
        try:
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024
            
            if memory_mb > self.config.max_memory_mb:
                self.logger.warning(f"Memory usage high: {memory_mb:.1f}MB")
                self.force_gc()
                return False
            return True
        except:
            return True
    
    def force_gc(self):
        """Force garbage collection"""
        gc.collect()
        self.logger.debug("Forced garbage collection")
    
    def increment_operation(self):
        """Increment operation counter and run GC if needed"""
        self.operation_count += 1
        if self.operation_count % self.config.gc_frequency == 0:
            self.force_gc()

class OptimizedGlassdoorScraper:
    """High-performance optimized Glassdoor scraper"""
    
    def __init__(self, config: OptimizedScraperConfig = None):
        self.config = config or OptimizedScraperConfig()
        self.logger = ScraperLogger()
        self.browser_pool = BrowserPool(self.config, self.logger)
        self.job_cache = JobCache(self.config)
        self.memory_manager = MemoryManager(self.config, self.logger)
        self.field_extractor = FieldExtractor(self.config, self.logger)
        self.human_behavior = HumanBehavior(self.logger)
        
        # Performance tracking
        self.stats = {
            'cache_hits': 0,
            'cache_misses': 0,
            'total_requests': 0,
            'avg_response_time': 0,
            'memory_cleanups': 0
        }
        
        self.logger.info(f"Initialized OptimizedGlassdoorScraper with {self.config.max_workers} workers")

    async def scrape_jobs_async(self, job_title: str, location: str, num_jobs: int = 5) -> Dict[str, Any]:
        """High-performance asynchronous job scraping"""
        start_time = time.time()
        self.stats['total_requests'] += 1

        metadata = {
            'start_time': datetime.now().isoformat(),
            'job_title': job_title,
            'location': location,
            'requested_jobs': num_jobs,
            'scraped_jobs': 0,
            'failed_jobs': 0,
            'cache_hits': 0,
            'errors': [],
            'performance_stats': {}
        }

        try:
            # Step 1: Collect job URLs with optimized browser
            url_collection_start = time.time()
            job_urls = await self._collect_job_urls_async(job_title, location, num_jobs * 2)
            url_collection_time = time.time() - url_collection_start

            if not job_urls:
                metadata['errors'].append('No job URLs found')
                return {'scraped_jobs': [], 'metadata': metadata}

            self.logger.info(f"Collected {len(job_urls)} URLs in {url_collection_time:.2f}s")

            # Step 2: Process jobs with high concurrency
            processing_start = time.time()
            jobs = await self._process_jobs_concurrent(job_urls[:num_jobs], metadata)
            processing_time = time.time() - processing_start

            # Update metadata
            total_time = time.time() - start_time
            metadata.update({
                'scraped_jobs': len(jobs),
                'execution_time': total_time,
                'end_time': datetime.now().isoformat(),
                'performance_stats': {
                    'url_collection_time': url_collection_time,
                    'job_processing_time': processing_time,
                    'avg_time_per_job': processing_time / len(jobs) if jobs else 0,
                    'cache_hit_rate': self.stats['cache_hits'] / max(1, self.stats['cache_hits'] + self.stats['cache_misses']),
                    'memory_usage_mb': psutil.Process().memory_info().rss / 1024 / 1024
                }
            })

            self.logger.info(f"Scraped {len(jobs)} jobs in {total_time:.2f}s (avg: {total_time/len(jobs):.2f}s per job)")
            return {'scraped_jobs': jobs, 'metadata': metadata}

        except Exception as e:
            self.logger.error(f"Async scraping failed: {str(e)}")
            metadata['errors'].append(str(e))
            metadata['execution_time'] = time.time() - start_time
            return {'scraped_jobs': [], 'metadata': metadata}

    async def _collect_job_urls_async(self, job_title: str, location: str, num_jobs: int) -> List[str]:
        """Asynchronous job URL collection with optimized browser"""
        with self.browser_pool.get_browser() as driver:
            try:
                # Perform search
                await self._perform_search_async(driver, job_title, location)

                # Collect URLs with intelligent pagination
                job_urls = set()
                max_pages = min(5, (num_jobs // 10) + 1)  # Estimate pages needed

                for page in range(max_pages):
                    if len(job_urls) >= num_jobs:
                        break

                    # Collect URLs from current page
                    page_urls = self._extract_job_urls_fast(driver)
                    job_urls.update(page_urls)

                    self.logger.debug(f"Page {page + 1}: Found {len(page_urls)} URLs (total: {len(job_urls)})")

                    # Try to go to next page
                    if page < max_pages - 1:
                        if not self._navigate_to_next_page(driver):
                            break
                        await asyncio.sleep(0.5)  # Brief pause between pages

                return list(job_urls)[:num_jobs]

            except Exception as e:
                self.logger.error(f"URL collection failed: {str(e)}")
                return []

    async def _perform_search_async(self, driver: webdriver.Chrome, job_title: str, location: str):
        """Optimized search with minimal delays"""
        try:
            driver.get("https://www.glassdoor.co.in/Job/index.htm")

            # Wait for Cloudflare if needed (with timeout)
            cloudflare_wait_start = time.time()
            while time.time() - cloudflare_wait_start < 10:  # Max 10s wait
                if "glassdoor" in driver.current_url.lower():
                    break
                await asyncio.sleep(0.5)

            # Fast form filling
            job_input = WebDriverWait(driver, 5).until(
                EC.presence_of_element_located((By.ID, "searchBar-jobTitle"))
            )
            location_input = driver.find_element(By.ID, "searchBar-location")

            # Clear and fill quickly
            job_input.clear()
            job_input.send_keys(job_title)
            location_input.clear()
            location_input.send_keys(location)

            # Submit immediately
            location_input.submit()

            # Wait for results
            WebDriverWait(driver, 10).until(
                lambda d: "/Job/" in d.current_url
            )

        except Exception as e:
            self.logger.error(f"Search failed: {str(e)}")
            raise

    def _extract_job_urls_fast(self, driver: webdriver.Chrome) -> Set[str]:
        """Fast job URL extraction using optimized JavaScript"""
        script = """
        const urls = new Set();
        const selectors = [
            'a[data-test="job-link"]',
            'a.JobCard_trackingLink__HMyun',
            'a[href*="jobListing.htm"]'
        ];

        for (const selector of selectors) {
            const links = document.querySelectorAll(selector);
            for (const link of links) {
                if (link.href && link.href.includes('glassdoor')) {
                    urls.add(link.href);
                }
            }
            if (urls.size > 0) break;
        }

        return Array.from(urls);
        """

        try:
            urls = driver.execute_script(script)
            return set(urls) if urls else set()
        except Exception as e:
            self.logger.debug(f"URL extraction failed: {e}")
            return set()

    def _navigate_to_next_page(self, driver: webdriver.Chrome) -> bool:
        """Navigate to next page if available"""
        try:
            # Try different next page selectors
            next_selectors = [
                'a[aria-label="Next"]',
                'a[data-test="pagination-next"]',
                '.next',
                'a:contains("Next")'
            ]

            for selector in next_selectors:
                try:
                    if selector.startswith('a:contains'):
                        # Use JavaScript for text-based selector
                        next_btn = driver.execute_script(
                            "return Array.from(document.querySelectorAll('a')).find(el => el.textContent.includes('Next'))"
                        )
                    else:
                        next_btn = driver.find_element(By.CSS_SELECTOR, selector)

                    if next_btn and next_btn.is_enabled():
                        driver.execute_script("arguments[0].click();", next_btn)
                        time.sleep(1)  # Wait for page load
                        return True
                except:
                    continue

            return False
        except Exception as e:
            self.logger.debug(f"Next page navigation failed: {e}")
            return False

    async def _process_jobs_concurrent(self, job_urls: List[str], metadata: Dict) -> List[Dict]:
        """High-performance concurrent job processing with streaming"""
        jobs = []
        semaphore = asyncio.Semaphore(self.config.max_concurrent_requests)

        async def process_single_job(url: str) -> Optional[Dict]:
            async with semaphore:
                return await self._extract_job_with_cache(url, metadata)

        # Create tasks for all jobs
        tasks = [process_single_job(url) for url in job_urls]

        # Process with streaming results
        completed_jobs = 0
        async for task in self._as_completed_async(tasks):
            try:
                job = await task
                if job:
                    jobs.append(job)
                    completed_jobs += 1

                    # Stream progress
                    if completed_jobs % 5 == 0:
                        self.logger.info(f"Processed {completed_jobs}/{len(job_urls)} jobs")

                    # Memory management
                    self.memory_manager.increment_operation()
                    if not self.memory_manager.check_memory():
                        self.logger.warning("Memory limit reached, stopping processing")
                        break

            except Exception as e:
                metadata['failed_jobs'] += 1
                self.logger.debug(f"Job processing failed: {e}")

        return jobs

    async def _as_completed_async(self, tasks):
        """Async generator for completed tasks"""
        pending = set(tasks)

        while pending:
            done, pending = await asyncio.wait(pending, return_when=asyncio.FIRST_COMPLETED)
            for task in done:
                yield task

    async def _extract_job_with_cache(self, url: str, metadata: Dict) -> Optional[Dict]:
        """Extract job with intelligent caching"""
        # Check cache first
        cached_job = self.job_cache.get(url)
        if cached_job:
            self.stats['cache_hits'] += 1
            metadata['cache_hits'] += 1
            return cached_job

        self.stats['cache_misses'] += 1

        # Extract job with optimized browser
        job = await self._extract_job_optimized(url)

        # Cache the result
        if job:
            self.job_cache.set(url, job)

        return job

    async def _extract_job_optimized(self, url: str) -> Optional[Dict]:
        """Optimized job extraction with minimal overhead"""
        with self.browser_pool.get_browser() as driver:
            try:
                # Navigate with timeout
                driver.set_page_load_timeout(self.config.page_load_timeout)
                driver.get(url)

                # Quick extraction using JavaScript
                job_data = self._extract_job_data_fast(driver)

                if job_data and job_data.get('title'):
                    # Process job description if available
                    if job_data.get('job_desc_html'):
                        soup = BeautifulSoup(job_data['job_desc_html'], 'html.parser')
                        extra_sections = self.field_extractor.extract_job_description_sections(soup)

                        if extra_sections:
                            job_data['job_description'] = self._generate_markdown_fast(extra_sections)
                            job_data['extra_sections'] = extra_sections

                    return job_data

                return None

            except Exception as e:
                self.logger.debug(f"Job extraction failed for {url}: {e}")
                return None

    def _extract_job_data_fast(self, driver: webdriver.Chrome) -> Dict:
        """Ultra-fast job data extraction using optimized JavaScript"""
        script = """
        const data = {};

        // Fast extraction with single DOM queries
        try {
            const title = document.querySelector('h1[data-test="job-title"], h1');
            if (title) data.title = title.textContent.trim();

            const company = document.querySelector('[data-test="employer-name"], .EmployerProfile_employerNameHeading__bXBYr');
            if (company) data.company_name = company.textContent.trim();

            const location = document.querySelector('[data-test="location"]');
            if (location) data.location = location.textContent.trim();

            const salary = document.querySelector('[data-test="detailSalary"]');
            if (salary) data.salary = salary.textContent.trim();

            const desc = document.querySelector('.JobDetails_jobDescription__uW_fK');
            if (desc) data.job_desc_html = desc.outerHTML;

            data.jd_url = window.location.href;

        } catch (e) {
            console.error('Extraction error:', e);
        }

        return data;
        """

        try:
            return driver.execute_script(script) or {}
        except Exception as e:
            self.logger.debug(f"Fast extraction failed: {e}")
            return {}

    def _generate_markdown_fast(self, sections: Dict) -> str:
        """Fast markdown generation with minimal processing"""
        md_parts = []

        # Priority sections first
        priority_sections = ['job description', 'most_relevant_skills', 'education', 'work_location']

        for section_key in priority_sections:
            if section_key in sections:
                content = sections[section_key]
                if content:
                    header = section_key.replace('_', ' ').title()
                    md_parts.append(f"## {header}")

                    if isinstance(content, list):
                        for item in content:
                            md_parts.append(f"• {item}")
                    else:
                        md_parts.append(str(content))

                    md_parts.append("")

        return "\n".join(md_parts).strip()

    def scrape_jobs_sync(self, job_title: str, location: str, num_jobs: int = 5) -> Dict[str, Any]:
        """Synchronous wrapper for async scraping"""
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        try:
            return loop.run_until_complete(self.scrape_jobs_async(job_title, location, num_jobs))
        finally:
            # Clean up cache periodically
            self.job_cache.cleanup_expired()

    def get_performance_stats(self) -> Dict:
        """Get current performance statistics"""
        return {
            **self.stats,
            'browser_pool_size': len(self.browser_pool.pool),
            'cache_size': len(self.job_cache.cache),
            'memory_usage_mb': psutil.Process().memory_info().rss / 1024 / 1024
        }

    def cleanup(self):
        """Clean up resources"""
        self.browser_pool.cleanup()
        self.memory_manager.force_gc()
        self.logger.info("Optimized scraper cleaned up")
