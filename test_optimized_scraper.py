#!/usr/bin/env python3
"""
Test script for the optimized Glassdoor scraper
"""

import requests
import json
import time

def test_scraper():
    """Test the optimized scraper"""
    
    print("🧪 Testing Optimized Glassdoor Scraper")
    print("=" * 50)
    
    # Test data
    test_data = {
        "job_title": "Data Scientist",
        "location": "Mumbai",
        "num_jobs": 2
    }
    
    print(f"📊 Test Parameters:")
    print(f"   Job Title: {test_data['job_title']}")
    print(f"   Location: {test_data['location']}")
    print(f"   Number of Jobs: {test_data['num_jobs']}")
    print()
    
    try:
        # Test the parallel endpoint
        print("🚀 Testing /scrape_jobs_parallel endpoint...")
        start_time = time.time()
        
        response = requests.post(
            "http://localhost:8000/glassdoor/scrape_jobs_parallel",
            json=test_data,
            timeout=120
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        print(f"⏱️  Response Time: {execution_time:.2f} seconds")
        print(f"📡 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"\n✅ SUCCESS!")
            print(f"📊 Results Summary:")
            print(f"   Jobs Found: {len(result.get('scraped_jobs', []))}")
            
            metadata = result.get('metadata', {})
            print(f"   Execution Time: {metadata.get('execution_time', 'N/A')}s")
            print(f"   Success Rate: {metadata.get('success_rate', 'N/A')}")
            
            # Show first job if available
            jobs = result.get('scraped_jobs', [])
            if jobs:
                first_job = jobs[0]
                print(f"\n📋 First Job Sample:")
                print(f"   Title: {first_job.get('title', 'N/A')}")
                print(f"   Company: {first_job.get('company_name', 'N/A')}")
                print(f"   Location: {first_job.get('location', 'N/A')}")
                print(f"   Salary: {first_job.get('salary', 'N/A')}")
                
                # Check for enhanced fields
                if first_job.get('most_relevant_skills'):
                    print(f"   Skills: {first_job.get('most_relevant_skills', [])[:3]}...")
                
                if first_job.get('extra_sections'):
                    print(f"   Enhanced Data: ✅ Available")
                else:
                    print(f"   Enhanced Data: ❌ Missing")
            
            # Test performance stats endpoint
            print(f"\n📈 Testing Performance Stats...")
            try:
                stats_response = requests.get("http://localhost:8000/glassdoor/performance_stats")
                if stats_response.status_code == 200:
                    stats = stats_response.json().get('performance_stats', {})
                    print(f"   Cache Hit Rate: {stats.get('cache_hit_rate', 0):.1%}")
                    print(f"   Memory Usage: {stats.get('memory_usage_mb', 0):.1f}MB")
                    print(f"   Browser Pool Size: {stats.get('browser_pool_size', 0)}")
                    print(f"   Cache Size: {stats.get('cache_size', 0)}")
                else:
                    print(f"   ❌ Performance stats failed: {stats_response.status_code}")
            except Exception as e:
                print(f"   ❌ Performance stats error: {e}")
            
        else:
            print(f"❌ FAILED!")
            print(f"Response: {response.text}")
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out (>120s)")
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - is the server running?")
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")

def test_performance_comparison():
    """Compare original vs optimized performance"""
    
    print("\n🏁 Performance Comparison Test")
    print("=" * 50)
    
    test_data = {
        "job_title": "Software Engineer",
        "location": "Bangalore",
        "num_jobs": 3
    }
    
    # Test original endpoint
    print("🐌 Testing original endpoint...")
    try:
        start_time = time.time()
        response = requests.post(
            "http://localhost:8000/glassdoor/scrape_jobs",
            json=test_data,
            timeout=180
        )
        original_time = time.time() - start_time
        original_jobs = len(response.json().get('scraped_jobs', [])) if response.status_code == 200 else 0
        
        print(f"   Time: {original_time:.2f}s")
        print(f"   Jobs: {original_jobs}")
        
    except Exception as e:
        print(f"   ❌ Original failed: {e}")
        original_time = float('inf')
        original_jobs = 0
    
    # Test optimized endpoint
    print("⚡ Testing optimized endpoint...")
    try:
        start_time = time.time()
        response = requests.post(
            "http://localhost:8000/glassdoor/scrape_jobs_parallel",
            json=test_data,
            timeout=180
        )
        optimized_time = time.time() - start_time
        optimized_jobs = len(response.json().get('scraped_jobs', [])) if response.status_code == 200 else 0
        
        print(f"   Time: {optimized_time:.2f}s")
        print(f"   Jobs: {optimized_jobs}")
        
        # Calculate improvement
        if original_time != float('inf') and original_time > 0:
            improvement = ((original_time - optimized_time) / original_time) * 100
            print(f"\n🎯 Performance Improvement: {improvement:.1f}%")
            
            if improvement >= 50:
                print("✅ TARGET ACHIEVED: 50%+ improvement!")
            elif improvement >= 30:
                print("⚠️  Good improvement, but below 50% target")
            else:
                print("❌ Improvement below expectations")
        
    except Exception as e:
        print(f"   ❌ Optimized failed: {e}")

if __name__ == "__main__":
    # Wait for server to be ready
    print("⏳ Waiting for server to be ready...")
    time.sleep(5)
    
    # Run tests
    test_scraper()
    test_performance_comparison()
    
    print("\n🎉 Testing completed!")
