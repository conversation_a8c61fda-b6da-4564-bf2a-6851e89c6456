#!/usr/bin/env python3
"""
Validation Script for Glassdoor Scraper Optimizations
Tests all performance improvements and validates target goals
"""

import time
import asyncio
import json
from datetime import datetime
from typing import Dict, List
import psutil

# Import optimized scraper
from optimized_glassdoor import OptimizedGlassdoorScraper, OptimizedScraperConfig

class OptimizationValidator:
    """Validates all performance optimizations"""
    
    def __init__(self):
        self.config = OptimizedScraperConfig()
        self.scraper = OptimizedGlassdoorScraper(self.config)
        self.validation_results = {}
        
    def run_full_validation(self) -> Dict:
        """Run comprehensive validation of all optimizations"""
        print("🔍 GLASSDOOR SCRAPER OPTIMIZATION VALIDATION")
        print("=" * 60)
        
        validation_results = {
            'timestamp': datetime.now().isoformat(),
            'target_goals': self._define_target_goals(),
            'test_results': {},
            'performance_metrics': {},
            'optimization_status': {},
            'recommendations': []
        }
        
        # Test 1: Browser Pool Performance
        print("\n1. 🌐 Testing Browser Instance Pooling...")
        validation_results['test_results']['browser_pool'] = self._test_browser_pool()
        
        # Test 2: Caching Efficiency
        print("\n2. 💾 Testing Job Caching System...")
        validation_results['test_results']['caching'] = self._test_caching_system()
        
        # Test 3: Concurrent Processing
        print("\n3. ⚡ Testing Concurrent Job Processing...")
        validation_results['test_results']['concurrency'] = self._test_concurrent_processing()
        
        # Test 4: Memory Management
        print("\n4. 🧠 Testing Memory Management...")
        validation_results['test_results']['memory'] = self._test_memory_management()
        
        # Test 5: Overall Performance
        print("\n5. 🚀 Testing Overall Performance...")
        validation_results['test_results']['overall'] = self._test_overall_performance()
        
        # Analyze results
        validation_results['optimization_status'] = self._analyze_optimization_status(validation_results)
        validation_results['recommendations'] = self._generate_recommendations(validation_results)
        
        # Generate final report
        self._generate_validation_report(validation_results)
        
        return validation_results
    
    def _define_target_goals(self) -> Dict:
        """Define performance target goals"""
        return {
            'time_reduction_percent': 50,  # 50% faster
            'concurrent_jobs': 8,  # Process 8 jobs simultaneously
            'cache_hit_rate': 0.3,  # 30% cache hit rate
            'memory_limit_mb': 2048,  # Stay under 2GB
            'browser_reuse_rate': 0.8,  # 80% browser reuse
            'success_rate': 0.9,  # 90% success rate
            'avg_time_per_job': 5.0  # Under 5 seconds per job
        }
    
    def _test_browser_pool(self) -> Dict:
        """Test browser instance pooling performance"""
        print("   Testing browser creation and reuse...")
        
        start_time = time.time()
        browser_creation_times = []
        
        # Test browser pool efficiency
        for i in range(5):
            browser_start = time.time()
            with self.scraper.browser_pool.get_browser() as browser:
                browser_creation_times.append(time.time() - browser_start)
                # Simulate some work
                browser.get("https://www.glassdoor.co.in")
                time.sleep(0.5)
        
        total_time = time.time() - start_time
        avg_creation_time = sum(browser_creation_times) / len(browser_creation_times)
        
        # Check pool statistics
        pool_size = len(self.scraper.browser_pool.pool)
        
        result = {
            'total_time': total_time,
            'avg_browser_creation_time': avg_creation_time,
            'pool_size': pool_size,
            'reuse_efficiency': pool_size / 5,  # How many browsers were reused
            'passed': avg_creation_time < 2.0 and pool_size > 0
        }
        
        print(f"   ✅ Browser pool test: {'PASSED' if result['passed'] else 'FAILED'}")
        print(f"      Average creation time: {avg_creation_time:.2f}s")
        print(f"      Pool size: {pool_size}")
        
        return result
    
    def _test_caching_system(self) -> Dict:
        """Test job caching efficiency"""
        print("   Testing job caching system...")
        
        test_url = "https://www.glassdoor.co.in/job-listing/test-job"
        test_job_data = {
            'title': 'Test Job',
            'company': 'Test Company',
            'location': 'Test Location'
        }
        
        # Test cache set/get
        cache_start = time.time()
        self.scraper.job_cache.set(test_url, test_job_data)
        cached_data = self.scraper.job_cache.get(test_url)
        cache_time = time.time() - cache_start
        
        # Test cache miss
        miss_data = self.scraper.job_cache.get("https://nonexistent-url.com")
        
        result = {
            'cache_set_get_time': cache_time,
            'cache_hit_success': cached_data == test_job_data,
            'cache_miss_success': miss_data is None,
            'cache_size': len(self.scraper.job_cache.cache),
            'passed': cached_data == test_job_data and miss_data is None and cache_time < 0.001
        }
        
        print(f"   ✅ Caching test: {'PASSED' if result['passed'] else 'FAILED'}")
        print(f"      Cache operation time: {cache_time:.4f}s")
        print(f"      Cache size: {result['cache_size']}")
        
        return result
    
    def _test_concurrent_processing(self) -> Dict:
        """Test concurrent job processing"""
        print("   Testing concurrent processing capabilities...")
        
        # Simulate concurrent job processing
        test_urls = [f"https://test-url-{i}.com" for i in range(8)]
        
        start_time = time.time()
        
        # Test with asyncio semaphore (simulating concurrent processing)
        async def simulate_concurrent_processing():
            semaphore = asyncio.Semaphore(self.config.max_concurrent_requests)
            
            async def process_job(url):
                async with semaphore:
                    await asyncio.sleep(0.1)  # Simulate processing time
                    return f"processed_{url}"
            
            tasks = [process_job(url) for url in test_urls]
            results = await asyncio.gather(*tasks)
            return results
        
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        
        results = loop.run_until_complete(simulate_concurrent_processing())
        processing_time = time.time() - start_time
        
        result = {
            'processing_time': processing_time,
            'jobs_processed': len(results),
            'avg_time_per_job': processing_time / len(results),
            'concurrency_achieved': len(results) == len(test_urls),
            'passed': processing_time < 1.0 and len(results) == len(test_urls)
        }
        
        print(f"   ✅ Concurrency test: {'PASSED' if result['passed'] else 'FAILED'}")
        print(f"      Processing time: {processing_time:.2f}s")
        print(f"      Jobs processed: {len(results)}")
        
        return result
    
    def _test_memory_management(self) -> Dict:
        """Test memory management efficiency"""
        print("   Testing memory management...")
        
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024
        
        # Simulate memory-intensive operations
        for i in range(100):
            self.scraper.memory_manager.increment_operation()
        
        # Force memory check
        memory_ok = self.scraper.memory_manager.check_memory()
        final_memory = process.memory_info().rss / 1024 / 1024
        memory_delta = final_memory - initial_memory
        
        result = {
            'initial_memory_mb': initial_memory,
            'final_memory_mb': final_memory,
            'memory_delta_mb': memory_delta,
            'memory_check_passed': memory_ok,
            'under_limit': final_memory < self.config.max_memory_mb,
            'passed': memory_ok and final_memory < self.config.max_memory_mb
        }
        
        print(f"   ✅ Memory test: {'PASSED' if result['passed'] else 'FAILED'}")
        print(f"      Memory usage: {final_memory:.1f}MB")
        print(f"      Memory delta: {memory_delta:.1f}MB")
        
        return result
    
    def _test_overall_performance(self) -> Dict:
        """Test overall scraper performance"""
        print("   Testing overall scraper performance...")
        
        start_time = time.time()
        
        try:
            # Run a small test scrape
            result = self.scraper.scrape_jobs_sync("Data Scientist", "Mumbai", 3)
            
            execution_time = time.time() - start_time
            jobs_scraped = len(result.get('scraped_jobs', []))
            success_rate = jobs_scraped / 3 if jobs_scraped <= 3 else 1.0
            avg_time_per_job = execution_time / jobs_scraped if jobs_scraped > 0 else float('inf')
            
            # Get performance stats
            perf_stats = self.scraper.get_performance_stats()
            
            test_result = {
                'execution_time': execution_time,
                'jobs_scraped': jobs_scraped,
                'success_rate': success_rate,
                'avg_time_per_job': avg_time_per_job,
                'performance_stats': perf_stats,
                'passed': (execution_time < 30 and 
                          success_rate >= 0.5 and 
                          avg_time_per_job < 10)
            }
            
            print(f"   ✅ Overall performance: {'PASSED' if test_result['passed'] else 'FAILED'}")
            print(f"      Execution time: {execution_time:.2f}s")
            print(f"      Jobs scraped: {jobs_scraped}")
            print(f"      Success rate: {success_rate:.1%}")
            
            return test_result
            
        except Exception as e:
            print(f"   ❌ Overall performance test failed: {str(e)}")
            return {
                'execution_time': float('inf'),
                'jobs_scraped': 0,
                'success_rate': 0,
                'error': str(e),
                'passed': False
            }
    
    def _analyze_optimization_status(self, results: Dict) -> Dict:
        """Analyze optimization status against targets"""
        targets = results['target_goals']
        tests = results['test_results']
        
        status = {}
        
        # Browser pool optimization
        browser_test = tests.get('browser_pool', {})
        status['browser_pooling'] = {
            'implemented': browser_test.get('pool_size', 0) > 0,
            'efficient': browser_test.get('avg_browser_creation_time', 10) < 2.0,
            'grade': 'A' if browser_test.get('passed', False) else 'C'
        }
        
        # Caching optimization
        cache_test = tests.get('caching', {})
        status['caching'] = {
            'implemented': cache_test.get('cache_size', 0) >= 0,
            'efficient': cache_test.get('cache_set_get_time', 1) < 0.001,
            'grade': 'A' if cache_test.get('passed', False) else 'C'
        }
        
        # Concurrency optimization
        concurrency_test = tests.get('concurrency', {})
        status['concurrency'] = {
            'implemented': concurrency_test.get('jobs_processed', 0) > 1,
            'efficient': concurrency_test.get('processing_time', 10) < 1.0,
            'grade': 'A' if concurrency_test.get('passed', False) else 'C'
        }
        
        # Memory management
        memory_test = tests.get('memory', {})
        status['memory_management'] = {
            'implemented': memory_test.get('memory_check_passed', False),
            'efficient': memory_test.get('under_limit', False),
            'grade': 'A' if memory_test.get('passed', False) else 'C'
        }
        
        # Overall performance
        overall_test = tests.get('overall', {})
        status['overall_performance'] = {
            'target_met': overall_test.get('avg_time_per_job', 100) < targets['avg_time_per_job'],
            'success_rate_met': overall_test.get('success_rate', 0) >= targets['success_rate'],
            'grade': 'A' if overall_test.get('passed', False) else 'C'
        }
        
        return status
    
    def _generate_recommendations(self, results: Dict) -> List[str]:
        """Generate optimization recommendations"""
        recommendations = []
        status = results['optimization_status']
        
        if not status.get('browser_pooling', {}).get('efficient', False):
            recommendations.append("Optimize browser pool creation time")
        
        if not status.get('caching', {}).get('efficient', False):
            recommendations.append("Improve caching system performance")
        
        if not status.get('concurrency', {}).get('efficient', False):
            recommendations.append("Increase concurrent processing efficiency")
        
        if not status.get('memory_management', {}).get('efficient', False):
            recommendations.append("Optimize memory usage and garbage collection")
        
        overall = status.get('overall_performance', {})
        if not overall.get('target_met', False):
            recommendations.append("Further optimize job processing speed")
        
        if not overall.get('success_rate_met', False):
            recommendations.append("Improve job extraction success rate")
        
        if not recommendations:
            recommendations.append("All optimizations are performing well!")
        
        return recommendations
    
    def _generate_validation_report(self, results: Dict):
        """Generate comprehensive validation report"""
        print("\n" + "="*60)
        print("📊 OPTIMIZATION VALIDATION REPORT")
        print("="*60)
        
        status = results['optimization_status']
        
        print(f"\n🎯 OPTIMIZATION STATUS:")
        for opt_name, opt_status in status.items():
            grade = opt_status.get('grade', 'N/A')
            implemented = opt_status.get('implemented', False)
            efficient = opt_status.get('efficient', False)
            
            status_icon = "✅" if grade == 'A' else "⚠️" if grade == 'B' else "❌"
            print(f"   {status_icon} {opt_name.replace('_', ' ').title()}: Grade {grade}")
            print(f"      Implemented: {'Yes' if implemented else 'No'}")
            print(f"      Efficient: {'Yes' if efficient else 'No'}")
        
        print(f"\n💡 RECOMMENDATIONS:")
        for i, rec in enumerate(results['recommendations'], 1):
            print(f"   {i}. {rec}")
        
        # Calculate overall grade
        grades = [s.get('grade', 'C') for s in status.values()]
        grade_scores = {'A': 4, 'B': 3, 'C': 2, 'D': 1, 'F': 0}
        avg_score = sum(grade_scores.get(g, 0) for g in grades) / len(grades)
        
        if avg_score >= 3.5:
            overall_grade = 'A'
        elif avg_score >= 2.5:
            overall_grade = 'B'
        else:
            overall_grade = 'C'
        
        print(f"\n🏆 OVERALL OPTIMIZATION GRADE: {overall_grade}")
        
        # Save detailed results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f'optimization_validation_{timestamp}.json'
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n💾 Detailed validation results saved to: {filename}")
    
    def cleanup(self):
        """Clean up resources"""
        try:
            self.scraper.cleanup()
        except:
            pass

def main():
    """Run optimization validation"""
    validator = OptimizationValidator()
    
    try:
        results = validator.run_full_validation()
        print("\n🎉 Validation completed successfully!")
        return results
    finally:
        validator.cleanup()

if __name__ == "__main__":
    main()
